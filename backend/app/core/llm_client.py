"""
傲基智能客服平台 - LLM客户端配置
支持OpenAI兼容API，包括DeepSeek等
"""

from typing import Optional, Dict, Any
from autogen_ext.models.openai import OpenAIChatCompletionClient
from app.core.config import settings


class LLMClientManager:
    """LLM客户端管理器"""
    
    def __init__(self):
        self._client: Optional[OpenAIChatCompletionClient] = None
        self._setup_client()
    
    def _setup_client(self) -> None:
        """设置LLM客户端"""
        if not settings.OPENAI_API_KEY:
            raise ValueError("OPENAI_API_KEY is required")
        
        model_config = {
            "model": settings.LLM_MODEL,
            "api_key": settings.OPENAI_API_KEY,
            "model_info": {
                "vision": False,
                "function_calling": True,
                "json_output": True,
                "family": "openai",
            },
        }
        
        # 如果设置了自定义API基础URL
        if settings.OPENAI_API_BASE:
            model_config["base_url"] = settings.OPENAI_API_BASE
        
        self._client = OpenAIChatCompletionClient(**model_config)
    
    @property
    def client(self) -> OpenAIChatCompletionClient:
        """获取LLM客户端实例"""
        if self._client is None:
            self._setup_client()
        return self._client
    
    def get_model_config(self) -> Dict[str, Any]:
        """获取模型配置信息"""
        return {
            "model": settings.LLM_MODEL,
            "temperature": settings.LLM_TEMPERATURE,
            "max_tokens": settings.LLM_MAX_TOKENS,
            "api_base": settings.OPENAI_API_BASE,
        }
    
    def validate_connection(self) -> bool:
        """验证LLM连接是否正常"""
        try:
            # 这里可以添加实际的连接测试逻辑
            return self._client is not None
        except Exception:
            return False


# 创建全局LLM客户端管理器实例
llm_manager = LLMClientManager()

# 导出客户端实例供其他模块使用
model_client = llm_manager.client
